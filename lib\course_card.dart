import 'package:flutter/material.dart';

class CourseCard extends StatelessWidget {
  final String title;
  final String level;
  final String lessons;
  final String image;
  final VoidCallback? onTap;

  const CourseCard({
    super.key,
    required this.title,
    required this.level,
    required this.lessons,
    required this.image,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(image, width: 80, height: 80, fit: BoxFit.cover),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Text(level),
                    Text(lessons),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
