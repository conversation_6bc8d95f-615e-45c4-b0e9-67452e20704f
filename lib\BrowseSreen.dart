import 'package:flutter/material.dart';
import 'category_tab.dart';
import 'course_card.dart';
import 'CourseDetailScreen.dart';

class BrowseScreen extends StatefulWidget {
  const BrowseScreen({super.key});

  @override
  State<BrowseScreen> createState() => _BrowseScreenState();
}

class _BrowseScreenState extends State<BrowseScreen> {
  String selectedCategory = 'Cooking';
  String searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> rawCourses = {
      'Cooking': [
        {
          'title': 'Cake Decorating',
          'level': 'Beginner',
          'lessons': '12 Lessons',
          'image': 'assets/b.png',
        },
        {
          'title': 'Serving',
          'level': 'Intermediate',
          'lessons': '4 Lessons',
          'image': 'assets/s.png',
        },
      ],
      'Sewing': [
        {
          'title': 'Hand Stitching',
          'level': 'Beginner',
          'lessons': '6 Lessons',
          'image': 'assets/sewing1.png',
        },
      ],
      'Languages': [
        {
          'title': 'English Basics',
          'level': 'Beginner',
          'lessons': '10 Lessons',
          'image': 'assets/lang1.png',
        },
      ],
    }[selectedCategory]!;

    final filteredCourses = rawCourses
        .where((course) =>
            course['title']!
                .toLowerCase()
                .contains(searchQuery.toLowerCase()))
        .toList();

    return Scaffold(
      backgroundColor: const Color(0xFFFFF0F5),
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 16),
            // ✅ Tabs
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                CategoryTab(
                  title: 'Cooking',
                  isActive: selectedCategory == 'Cooking',
                  onTap: () => setState(() {
                    selectedCategory = 'Cooking';
                    searchQuery = '';
                  }),
                ),
                CategoryTab(
                  title: 'Sewing',
                  isActive: selectedCategory == 'Sewing',
                  onTap: () => setState(() {
                    selectedCategory = 'Sewing';
                    searchQuery = '';
                  }),
                ),
                CategoryTab(
                  title: 'Languages',
                  isActive: selectedCategory == 'Languages',
                  onTap: () => setState(() {
                    selectedCategory = 'Languages';
                    searchQuery = '';
                  }),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // ✅ Search
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Rechercher une formation...',
                  prefixIcon: const Icon(Icons.search),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    searchQuery = value;
                  });
                },
              ),
            ),
            const SizedBox(height: 16),

            // ✅ Results
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: filteredCourses.length,
                itemBuilder: (context, index) {
                  final course = filteredCourses[index];
                  return CourseCard(
                    title: course['title']!,
                    level: course['level']!,
                    lessons: course['lessons']!,
                    image: course['image']!,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => CourseDetailScreen(
                            title: course['title']!,
                            level: course['level']!,
                            lessons: course['lessons']!,
                            image: course['image']!,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
