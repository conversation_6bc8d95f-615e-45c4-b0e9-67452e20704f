import 'package:flutter/material.dart';
import 'package:home_craft/BrowseSreen.dart';
import 'package:home_craft/InterestsScreen.dart';
import 'package:home_craft/my_courses_screen.dart';
import 'package:home_craft/HomeScreen.dart'; // Séparé si tu l'as extrait



void main() {
  runApp(const HomeCraftApp());
}

class HomeCraftApp extends StatelessWidget {
  const HomeCraftApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'HomeCraft',
      home: MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int currentIndex = 0;

  final List<Widget> pages = const [
    HomeScreen(),
    BrowseScreen(),
    MyCoursesScreen(),
    InterestsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: pages[currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        selectedItemColor: Colors.pinkAccent,
        unselectedItemColor: Colors.grey,
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          setState(() {
            currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: "Home"),
          BottomNavigationBarItem(icon: Icon(Icons.grid_view), label: "Browse"),
          BottomNavigationBarItem(icon: Icon(Icons.play_circle), label: "My Courses"),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: "Interests"),
        ],
      ),
    );
  }
}
