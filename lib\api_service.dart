import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = "http://10.0.2.2/homecraft_api"; // Android Emulator

  static Future<Map<String, dynamic>> getUserInfo(int userId) async {
    final response = await http.get(Uri.parse('$baseUrl/get_user_info.php?user_id=$userId'));
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Erreur infos utilisateur');
    }
  }

  static Future<List<Map<String, dynamic>>> getUserCourses(int userId) async {
    final response = await http.get(Uri.parse('$baseUrl/get_user_courses.php?user_id=$userId'));
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data);
    } else {
      throw Exception('Erreur cours');
    }
  }

  static Future<String> uploadImage(File imageFile, int userId) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/upload_image.php'),
    );
    request.fields['user_id'] = userId.toString();
    request.files.add(await http.MultipartFile.fromPath('image', imageFile.path));
    final response = await request.send();

    if (response.statusCode == 200) {
      return 'Succès';
    } else {
      throw Exception('Erreur envoi image');
    }
  }
}
