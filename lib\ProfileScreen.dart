import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'api_service.dart';

class ProfileScreen extends StatefulWidget {
  final int userId;
  final String userName;
  final String userEmail;
  final String? userImage;

  const ProfileScreen({
    super.key,
    required this.userId,
    required this.userName,
    required this.userEmail,
    this.userImage,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  List<Map<String, dynamic>> userCourses = [];
  File? selectedImage;
  String? uploadedImagePath;

  @override
  void initState() {
    super.initState();
    loadCourses();
  }

  Future<void> loadCourses() async {
    final courses = await ApiService.getUserCourses(widget.userId);
    setState(() {
      userCourses = courses;
    });
  }

  Future<void> pickImageAndUpload() async {
    final picker = ImagePicker();
    final picked = await picker.pickImage(source: ImageSource.gallery);
    if (picked != null) {
      final file = File(picked.path);
      await ApiService.uploadImage(file, widget.userId);
      setState(() => uploadedImagePath = file.path);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Image uploadée avec succès')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final String imagePath = uploadedImagePath ??
        (widget.userImage != null ? '${ApiService.baseUrl}/${widget.userImage}' : '');

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.pinkAccent,
        title: const Text('Mon Profil', style: TextStyle(color: Colors.white)),
        centerTitle: true,
      ),
      backgroundColor: const Color(0xFFFFF0F5),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          Center(
            child: Column(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundImage: imagePath.isNotEmpty
                      ? NetworkImage(imagePath)
                      : const AssetImage('assets/person.png') as ImageProvider,
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: pickImageAndUpload,
                  icon: const Icon(Icons.upload),
                  label: const Text("Changer la photo"),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.pinkAccent),
                ),
                const SizedBox(height: 12),
                Text(widget.userName, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                Text(widget.userEmail, style: const TextStyle(color: Colors.black54)),
              ],
            ),
          ),
          const SizedBox(height: 32),
          const Text('Mes cours suivis', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ...userCourses.map((course) => Card(
                child: ListTile(
                  title: Text(course['title']),
                  subtitle: Text("Type: ${course['type']}"),
                  trailing: const Icon(Icons.play_arrow),
                ),
              )),
        ],
      ),
    );
  }
}
