import 'package:flutter/material.dart';
import 'course_card.dart'; // si tu utilises déjà CourseCard dans BrowseScreen

class MyCoursesScreen extends StatelessWidget {
  const MyCoursesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFF0F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFFF0F5),
        elevation: 0,
        title: const Text(
          'My Courses',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: const [
          CourseCard(
            title: 'Baking Masterclass',
            level: 'In Progress',
            lessons: '8/12 Lessons',
            image: 'assets/b.png',
          ),
          CourseCard(
            title: 'Sewing Basics',
            level: 'Completed',
            lessons: '5/5 Lessons',
            image: 'assets/s.png',
          ),
        ],
      ),
    );
  }
}
